type RawMessage = {
  imei: string;
  status: string;
  satelliteTimestamp: string | Date;
  latitude?: number;
  longitude?: number;
  speed?: number;
  direction?: number;
  batteryPercentage?: number;
  [key: string]: any;
};

export function sanitizeMessages(messages: RawMessage[]): RawMessage[] {
  // Ordina per IMEI e timestamp
  messages.sort((a, b) => {
    if (a.imei !== b.imei) return a.imei.localeCompare(b.imei);
    return new Date(a.satelliteTimestamp).getTime() - new Date(b.satelliteTimestamp).getTime();
  });

  let currentTripId: string | null = null;
  let lastImei: string | null = null;
  let tripCounter: number = 0;

  return messages.map((msg) => {
    // Conversione robusta del timestamp
    const timestamp = typeof msg.satelliteTimestamp === 'string'
      ? new Date(msg.satelliteTimestamp)
      : msg.satelliteTimestamp;

    // Assicura che i campi numerici siano presenti e validi
    const latitude = typeof msg.latitude === 'number' ? msg.latitude : 0;
    const longitude = typeof msg.longitude === 'number' ? msg.longitude : 0;
    const speed = typeof msg.speed === 'number' ? msg.speed : 0;
    const direction = typeof msg.direction === 'number' ? msg.direction : 0;
    const batteryPercentage = typeof msg.batteryPercentage === 'number' ? msg.batteryPercentage : 0;

    // Se cambio IMEI, resetto il trip
    if (msg.imei !== lastImei) {
      currentTripId = null;
      tripCounter = 0;
      lastImei = msg.imei;
    }

    if (msg.status === 'Start') {
      tripCounter++;
      currentTripId = `trip_${msg.imei}_${tripCounter}`;
      return { ...msg, satelliteTimestamp: timestamp, tripId: currentTripId, latitude, longitude, speed, direction, batteryPercentage };
    }

    if (msg.status === 'End') {
      // Se non c'è un trip aperto, ne crea uno "orfano"
      if (!currentTripId) {
        tripCounter++;
        currentTripId = `trip_${msg.imei}_${tripCounter}`;
      }
      const result = { ...msg, satelliteTimestamp: timestamp, tripId: currentTripId, latitude, longitude, speed, direction, batteryPercentage };
      currentTripId = null; // Chiude il viaggio
      return result;
    }

    // Messaggi intermedi (Fixed/No Fixed)
    return { ...msg, satelliteTimestamp: timestamp, tripId: currentTripId, latitude, longitude, speed, direction, batteryPercentage };
  });
} 