/**
 * Test per verificare la nuova logica ottimizzata dei messaggi retroattivi
 * Questo test verifica che:
 * 1. I messaggi vengano raggruppati correttamente in base ai gap temporali
 * 2. Gli stati vengano assegnati correttamente (Start/Fixed/No Fixed/End)
 * 3. I messaggi fittizi vengano creati solo per messaggi isolati
 */

import { storage } from './server/storage.ts';

async function testRetroactiveLogic() {
  console.log('🧪 Inizio test della logica retroattiva ottimizzata...\n');

  // Pulisci lo storage
  storage.messages.clear();
  storage.retroactiveBuffers.clear();

  const testImei = '123456789012345';
  
  // Test Case 1: Sequenza continua (gap ≤ 5 min)
  console.log('📋 Test Case 1: Sequenza continua (gap ≤ 5 min)');
  
  const baseTime = new Date('2024-01-15T10:00:00Z');
  
  // Simula messaggi retroattivi con gap di 1 minuto
  const continuousMessages = [
    {
      imei: testImei,
      satelliteTimestamp: new Date(baseTime.getTime() + 0 * 60000), // 10:00
      latitude: 45.4642,
      longitude: 9.1900,
      speed: 50,
      direction: 90,
      batteryPercentage: 85,
      status: 'Fixed'
    },
    {
      imei: testImei,
      satelliteTimestamp: new Date(baseTime.getTime() + 1 * 60000), // 10:01
      latitude: 45.4652,
      longitude: 9.1910,
      speed: 52,
      direction: 92,
      batteryPercentage: 84,
      status: 'Fixed'
    },
    {
      imei: testImei,
      satelliteTimestamp: new Date(baseTime.getTime() + 2 * 60000), // 10:02
      latitude: 45.4662,
      longitude: 9.1920,
      speed: 48,
      direction: 88,
      batteryPercentage: 83,
      status: 'No Fixed'
    },
    {
      imei: testImei,
      satelliteTimestamp: new Date(baseTime.getTime() + 3 * 60000), // 10:03
      latitude: 45.4672,
      longitude: 9.1930,
      speed: 55,
      direction: 95,
      batteryPercentage: 82,
      status: 'Fixed'
    }
  ];

  // Aggiungi i messaggi
  for (const msg of continuousMessages) {
    await storage.addSatelliteMessage(msg);
  }

  // Forza il flush del buffer
  storage.flushRetroactiveBuffer(testImei);

  // Verifica i risultati
  const messages1 = Array.from(storage.messages.values())
    .filter(m => m.imei === testImei)
    .sort((a, b) => new Date(a.satelliteTimestamp).getTime() - new Date(b.satelliteTimestamp).getTime());

  console.log(`✅ Messaggi creati: ${messages1.length}`);
  console.log('📊 Stati dei messaggi:');
  messages1.forEach((msg, index) => {
    const time = new Date(msg.satelliteTimestamp).toISOString().substr(11, 8);
    console.log(`   ${index + 1}. ${time} → stato: '${msg.status}', tripId: ${msg.tripId?.substr(-10)}`);
  });

  // Verifica che ci sia un solo viaggio
  const tripIds1 = [...new Set(messages1.map(m => m.tripId))];
  console.log(`✅ Numero di viaggi: ${tripIds1.length} (atteso: 1)`);
  
  // Verifica gli stati
  const startMessages1 = messages1.filter(m => m.status === 'Start');
  const endMessages1 = messages1.filter(m => m.status === 'End');
  const intermediateMessages1 = messages1.filter(m => m.status === 'Fixed' || m.status === 'No Fixed');
  
  console.log(`✅ Messaggi Start: ${startMessages1.length} (atteso: 1)`);
  console.log(`✅ Messaggi End: ${endMessages1.length} (atteso: 1)`);
  console.log(`✅ Messaggi intermedi: ${intermediateMessages1.length} (atteso: 2)`);
  console.log('');

  // Test Case 2: Gap > 5 minuti tra gruppi
  console.log('📋 Test Case 2: Gap > 5 minuti tra gruppi');
  
  // Pulisci per il secondo test
  storage.messages.clear();
  
  const gapMessages = [
    // Primo gruppo
    {
      imei: testImei,
      satelliteTimestamp: new Date(baseTime.getTime() + 0 * 60000), // 10:00
      latitude: 45.4642,
      longitude: 9.1900,
      speed: 50,
      direction: 90,
      batteryPercentage: 85,
      status: 'Fixed'
    },
    {
      imei: testImei,
      satelliteTimestamp: new Date(baseTime.getTime() + 1 * 60000), // 10:01
      latitude: 45.4652,
      longitude: 9.1910,
      speed: 52,
      direction: 92,
      batteryPercentage: 84,
      status: 'Fixed'
    },
    // GAP > 5 minuti
    {
      imei: testImei,
      satelliteTimestamp: new Date(baseTime.getTime() + 8 * 60000), // 10:08 (gap di 7 minuti)
      latitude: 45.4700,
      longitude: 9.2000,
      speed: 60,
      direction: 100,
      batteryPercentage: 80,
      status: 'Fixed'
    },
    {
      imei: testImei,
      satelliteTimestamp: new Date(baseTime.getTime() + 9 * 60000), // 10:09
      latitude: 45.4710,
      longitude: 9.2010,
      speed: 58,
      direction: 98,
      batteryPercentage: 79,
      status: 'No Fixed'
    }
  ];

  // Aggiungi i messaggi
  for (const msg of gapMessages) {
    await storage.addSatelliteMessage(msg);
  }

  // Forza il flush del buffer
  storage.flushRetroactiveBuffer(testImei);

  // Verifica i risultati
  const messages2 = Array.from(storage.messages.values())
    .filter(m => m.imei === testImei)
    .sort((a, b) => new Date(a.satelliteTimestamp).getTime() - new Date(b.satelliteTimestamp).getTime());

  console.log(`✅ Messaggi creati: ${messages2.length}`);
  console.log('📊 Stati dei messaggi:');
  messages2.forEach((msg, index) => {
    const time = new Date(msg.satelliteTimestamp).toISOString().substr(11, 8);
    console.log(`   ${index + 1}. ${time} → stato: '${msg.status}', tripId: ${msg.tripId?.substr(-10)}`);
  });

  // Verifica che ci siano due viaggi
  const tripIds2 = [...new Set(messages2.map(m => m.tripId))];
  console.log(`✅ Numero di viaggi: ${tripIds2.length} (atteso: 2)`);
  console.log('');

  // Test Case 3: Messaggio isolato
  console.log('📋 Test Case 3: Messaggio isolato');
  
  // Pulisci per il terzo test
  storage.messages.clear();
  
  const isolatedMessage = {
    imei: testImei,
    satelliteTimestamp: new Date(baseTime.getTime() + 20 * 60000), // 10:20
    latitude: 45.5000,
    longitude: 9.3000,
    speed: 40,
    direction: 80,
    batteryPercentage: 75,
    status: 'Fixed'
  };

  await storage.addSatelliteMessage(isolatedMessage);
  
  // Forza il flush del buffer
  storage.flushRetroactiveBuffer(testImei);

  // Verifica i risultati
  const messages3 = Array.from(storage.messages.values())
    .filter(m => m.imei === testImei)
    .sort((a, b) => new Date(a.satelliteTimestamp).getTime() - new Date(b.satelliteTimestamp).getTime());

  console.log(`✅ Messaggi creati: ${messages3.length} (atteso: 2 - originale + fittizio END)`);
  console.log('📊 Stati dei messaggi:');
  messages3.forEach((msg, index) => {
    const time = new Date(msg.satelliteTimestamp).toISOString().substr(11, 8);
    const isFictitious = msg.id !== isolatedMessage.id ? ' (FITTIZIO)' : '';
    console.log(`   ${index + 1}. ${time} → stato: '${msg.status}', tripId: ${msg.tripId?.substr(-10)}${isFictitious}`);
  });

  const startMessages3 = messages3.filter(m => m.status === 'Start');
  const endMessages3 = messages3.filter(m => m.status === 'End');
  
  console.log(`✅ Messaggi Start: ${startMessages3.length} (atteso: 1)`);
  console.log(`✅ Messaggi End: ${endMessages3.length} (atteso: 1)`);
  console.log(`✅ Messaggio End è fittizio: ${endMessages3[0]?.id !== isolatedMessage.id ? 'Sì' : 'No'} (atteso: Sì)`);

  console.log('\n🎉 Test completato!');
}

// Esegui il test
testRetroactiveLogic().catch(console.error);
