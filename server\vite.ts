import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { createServer as createViteServer, createLogger } from "vite";
import { type Server } from "http";
import viteConfig from "../vite.config";
import { nanoid } from "nanoid";
import { readFileSync } from "node:fs";
import { join, resolve } from "node:path";

const viteLogger = createLogger();

export function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

export async function setupVite(app: Express, server: Server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: ["localhost"],
  };

  const vite = await createViteServer({
    ...viteConfig,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      },
    },
    server: serverOptions,
    appType: "custom",
  });

  // Serve static files before Vite middleware
  app.use('/assets', express.static(path.join(process.cwd(), 'client/assets'), {
    setHeaders: (res, filePath) => {
      if (filePath.endsWith('.js') || filePath.endsWith('.mjs')) {
        res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
      } else if (filePath.endsWith('.css')) {
        res.setHeader('Content-Type', 'text/css; charset=utf-8');
      } else if (filePath.endsWith('.png')) {
        res.setHeader('Content-Type', 'image/png');
      } else if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
        res.setHeader('Content-Type', 'image/jpeg');
      }
    }
  }));
  
  app.use('/manifest.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    res.sendFile(path.join(process.cwd(), 'client/manifest.json'));
  });
  
  app.use('/service-worker.js', (req, res) => {
    res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
    res.sendFile(path.join(process.cwd(), 'client/service-worker.js'));
  });

  app.use(vite.middlewares);
  app.use("*", async (req, res, next) => {
    const url = req.originalUrl;

    try {
      const clientTemplate = path.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html",
      );

      // always reload the index.html file from disk incase it changes
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html; charset=utf-8" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e as Error);
      next(e);
    }
  });
}

export function serveStatic(app: Express, isProd: boolean) {
  if (isProd) {
    const distPath = resolve("dist", "public");

    // Serve assets directory with proper MIME types and caching
    app.use('/assets', express.static(join(distPath, 'assets'), {
      setHeaders: (res, filePath) => {
        if (filePath.endsWith('.js') || filePath.endsWith('.mjs')) {
          res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
          res.setHeader('Cache-Control', 'public, max-age=********, immutable'); // Cache for 1 year
        } else if (filePath.endsWith('.css')) {
          res.setHeader('Content-Type', 'text/css; charset=utf-8');
          res.setHeader('Cache-Control', 'public, max-age=********, immutable'); // Cache for 1 year
        } else if (filePath.endsWith('.png') || filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
          res.setHeader('Cache-Control', 'public, max-age=********, immutable');
        }
      }
    }));

    // Serve manifest.json and service-worker.js with specific routes
    app.get('/manifest.json', (req, res) => {
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
      res.sendFile(join(distPath, 'manifest.json'));
    });

    app.get('/service-worker.js', (req, res) => {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate'); // Don't cache service worker
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.sendFile(join(distPath, 'service-worker.js'));
    });

    // Middleware to handle requests for non-existent asset files
    app.use((req, res, next) => {
      if (req.path.startsWith('/assets/')) {
        const filePath = join(distPath, req.path);
        if (!fs.existsSync(filePath)) {
          log(`Asset file not found: ${req.path}`);

          // Try to find a similar file (for cache-busting issues)
          const fileName = path.basename(req.path);
          const fileExt = path.extname(fileName);
          const baseName = fileName.replace(fileExt, '');

          // Look for files with similar names but different hashes
          const assetsDir = join(distPath, 'assets');
          if (fs.existsSync(assetsDir)) {
            const files = fs.readdirSync(assetsDir);
            const similarFile = files.find(file => {
              const fileBaseName = file.replace(path.extname(file), '');
              // Check if it's the same type of file (index.js, index.css, etc.)
              return fileBaseName.startsWith('index-') && file.endsWith(fileExt);
            });

            if (similarFile) {
              log(`Redirecting ${req.path} to /assets/${similarFile}`);
              res.redirect(301, `/assets/${similarFile}`);
              return;
            }
          }

          if (req.path.endsWith('.js')) {
            res.status(404).setHeader('Content-Type', 'text/plain').send('JavaScript file not found');
          } else if (req.path.endsWith('.css')) {
            res.status(404).setHeader('Content-Type', 'text/plain').send('CSS file not found');
          } else {
            res.status(404).setHeader('Content-Type', 'text/plain').send('Asset file not found');
          }
          return;
        }
      }
      next();
    });

    // Serve other static files (but not index.html)
    app.use(express.static(distPath, {
      index: false, // Don't serve index.html automatically
      setHeaders: (res, filePath) => {
        if (filePath.endsWith('.js') || filePath.endsWith('.mjs')) {
          res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
          res.setHeader('Cache-Control', 'public, max-age=********, immutable'); // Cache for 1 year
        } else if (filePath.endsWith('.json')) {
          res.setHeader('Content-Type', 'application/json; charset=utf-8');
        } else if (filePath.endsWith('.css')) {
          res.setHeader('Content-Type', 'text/css; charset=utf-8');
          res.setHeader('Cache-Control', 'public, max-age=********, immutable'); // Cache for 1 year
        } else if (filePath.endsWith('.png') || filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
          res.setHeader('Cache-Control', 'public, max-age=********, immutable');
        }
      }
    }));

    // Fallback to index.html for SPA routing - this must be last
    app.get("*", (req, res) => {
      // Set extremely strong no-cache headers to prevent any caching of index.html
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate, proxy-revalidate, max-age=0');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT');
      res.setHeader('Surrogate-Control', 'no-store');
      res.setHeader('Content-Type', 'text/html; charset=utf-8');

      // Add timestamp-based ETag to force revalidation
      const timestamp = Date.now();
      const etag = `"${timestamp}"`;
      res.setHeader('ETag', etag);

      // Always send a fresh copy - never return 304
      res.setHeader('Last-Modified', new Date().toUTCString());

      // Add custom header to help with debugging
      res.setHeader('X-Served-At', new Date().toISOString());

      // Add headers to prevent proxy caching
      res.setHeader('X-Accel-Expires', '0');
      res.setHeader('Vary', '*');

      // Read and modify the HTML to add cache-busting timestamp
      try {
        const indexPath = join(distPath, "index.html");
        let html = fs.readFileSync(indexPath, 'utf-8');

        // Add a cache-busting script that forces reload if assets fail to load
        const cacheBustingScript = `
        <script>
          // Cache busting script - forces reload if JS modules fail to load
          window.addEventListener('error', function(e) {
            if (e.target && e.target.tagName === 'SCRIPT' && e.target.src) {
              console.warn('Failed to load script:', e.target.src);
              console.warn('Forcing page reload to clear cache...');
              // Clear all caches before reload
              if ('caches' in window) {
                caches.keys().then(function(names) {
                  for (let name of names) {
                    caches.delete(name);
                  }
                });
              }
              setTimeout(() => {
                window.location.reload(true);
              }, 1000);
            }
          }, true);

          // Also handle CSS load errors
          window.addEventListener('error', function(e) {
            if (e.target && e.target.tagName === 'LINK' && e.target.href && e.target.rel === 'stylesheet') {
              console.warn('Failed to load stylesheet:', e.target.href);
              console.warn('Forcing page reload to clear cache...');
              // Clear all caches before reload
              if ('caches' in window) {
                caches.keys().then(function(names) {
                  for (let name of names) {
                    caches.delete(name);
                  }
                });
              }
              setTimeout(() => {
                window.location.reload(true);
              }, 1000);
            }
          }, true);
        </script>`;

        html = html.replace('</head>', cacheBustingScript + '\n  </head>');

        res.send(html);
      } catch (error) {
        log(`Error reading index.html: ${error}`);
        res.sendFile(join(distPath, "index.html"));
      }
    });
  } else {
    // This shouldn't be called in development mode
    console.log('Warning: serveStatic called in development mode');
  }
}

export async function registerVite(app: Express) {
  if (process.env.NODE_ENV === "production") {
    const distPath = resolve("client");
    app.use(express.static(distPath, {
      setHeaders: (res, path) => {
        if (path.endsWith('.js') || path.endsWith('.mjs')) {
          res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
        } else if (path.endsWith('.json')) {
          res.setHeader('Content-Type', 'application/json; charset=utf-8');
        } else if (path.endsWith('.css')) {
          res.setHeader('Content-Type', 'text/css; charset=utf-8');
        } else if (path.endsWith('.html')) {
          res.setHeader('Content-Type', 'text/html; charset=utf-8');
        }
      }
    }));
    app.get("*", (req, res) => {
      res.sendFile(resolve(distPath, "index.html"));
    });
  } else {
    const { createServer } = await import("vite");
    const vite = await createServer({
      server: { middlewareMode: true },
      appType: "spa", 
      root: "client",
    });
    app.use(vite.ssrFixStacktrace);
    app.use("*", vite.middlewares);
  }
}
