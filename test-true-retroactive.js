/**
 * Test per messaggi veramente retroattivi - messaggi che arrivano dopo che il viaggio è già chiuso
 */

import net from 'net';

const SERVER_HOST = 'localhost';
const SERVER_PORT = 8090;
const TEST_IMEI = '987654321098765';

function createTestMessage(imei, timestamp, lat, lng, speed) {
  const date = new Date(timestamp);
  const timeStr = date.toISOString().split('T')[1].replace(/:/g, '').split('.')[0]; // HHMMSS
  const dateStr = String(date.getDate()).padStart(2, '0') + 
                  String(date.getMonth() + 1).padStart(2, '0') + 
                  String(date.getFullYear()).slice(-2); // DDMMYY
  
  // Converti coordinate decimali in formato DDMM.MMMM
  const latDeg = Math.floor(Math.abs(lat));
  const latMin = (Math.abs(lat) - latDeg) * 60;
  const latStr = String(latDeg).padStart(2, '0') + latMin.toFixed(4).padStart(7, '0');
  
  const lngDeg = Math.floor(Math.abs(lng));
  const lngMin = (Math.abs(lng) - lngDeg) * 60;
  const lngStr = String(lngDeg).padStart(3, '0') + lngMin.toFixed(4).padStart(7, '0');
  
  return `*MAKER,${imei},V1,${timeStr},A,${latStr},N,${lngStr},E,${speed},0,${dateStr},F,0,0,0,0,0#`;
}

async function testTrueRetroactiveMessages() {
  console.log('🧪 Testing TRUE retroactive messages...');
  
  // FASE 1: Crea un viaggio normale e chiudilo
  console.log('\n📡 FASE 1: Creating normal trip...');
  
  const client1 = new net.Socket();
  
  await new Promise((resolve, reject) => {
    client1.connect(SERVER_PORT, SERVER_HOST, () => {
      console.log('✅ Connected for normal trip');
      
      // Invia alcuni messaggi normali
      const normalMessages = [
        createTestMessage(TEST_IMEI, '2025-01-30T08:00:00Z', 45.4642, 9.1900, 50),
        createTestMessage(TEST_IMEI, '2025-01-30T08:05:00Z', 45.4650, 9.1920, 55),
        createTestMessage(TEST_IMEI, '2025-01-30T08:10:00Z', 45.4660, 9.1940, 60)
      ];
      
      let messageIndex = 0;
      const sendNext = () => {
        if (messageIndex >= normalMessages.length) {
          console.log('📦 Normal messages sent, disconnecting...');
          setTimeout(() => client1.destroy(), 1000);
          return;
        }
        
        console.log(`📤 Sending normal message ${messageIndex + 1}/${normalMessages.length}`);
        client1.write(normalMessages[messageIndex]);
        messageIndex++;
        setTimeout(sendNext, 500);
      };
      
      sendNext();
    });
    
    client1.on('close', () => {
      console.log('🔌 Normal trip connection closed');
      resolve();
    });
    
    client1.on('error', reject);
  });
  
  // Aspetta un po' per assicurarsi che il viaggio sia chiuso
  console.log('⏳ Waiting 3 seconds for trip to be fully processed...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // FASE 2: Invia messaggi retroattivi che dovrebbero finire nel buffer
  console.log('\n📡 FASE 2: Sending retroactive messages...');
  
  const client2 = new net.Socket();
  
  await new Promise((resolve, reject) => {
    client2.connect(SERVER_PORT, SERVER_HOST, () => {
      console.log('✅ Connected for retroactive messages');
      
      // Messaggi retroattivi - timestamp precedenti al viaggio normale ma arrivano dopo
      const retroactiveMessages = [
        createTestMessage(TEST_IMEI, '2025-01-30T07:30:00Z', 45.4600, 9.1850, 40), // Prima del viaggio normale
        createTestMessage(TEST_IMEI, '2025-01-30T07:35:00Z', 45.4610, 9.1860, 45), // Prima del viaggio normale
        createTestMessage(TEST_IMEI, '2025-01-30T07:45:00Z', 45.4620, 9.1870, 48), // Prima del viaggio normale - gap > 5 min
      ];
      
      let messageIndex = 0;
      const sendNext = () => {
        if (messageIndex >= retroactiveMessages.length) {
          console.log('📦 Retroactive messages sent, waiting before disconnect...');
          setTimeout(() => {
            console.log('🔌 Disconnecting to trigger buffer flush...');
            client2.destroy();
          }, 2000);
          return;
        }
        
        const timestamps = ['2025-01-30T07:30:00Z', '2025-01-30T07:35:00Z', '2025-01-30T07:45:00Z'];
        console.log(`📤 Sending retroactive message ${messageIndex + 1}/${retroactiveMessages.length}`);
        console.log(`   Timestamp: ${timestamps[messageIndex]}`);
        client2.write(retroactiveMessages[messageIndex]);
        messageIndex++;
        setTimeout(sendNext, 1000);
      };
      
      sendNext();
    });
    
    client2.on('close', () => {
      console.log('🔌 Retroactive messages connection closed');
      console.log('✅ Test completed! Check server logs for buffer processing.');
      resolve();
    });
    
    client2.on('error', reject);
  });
}

// Esegui il test
testTrueRetroactiveMessages().catch(console.error);
