# Configurazione Nginx Proxy Manager per risolvere problemi di cache
# Copia e incolla queste direttive nella sezione "Advanced" del tuo Proxy Host

# OPZIONE 1: Configurazione completa con cache selettiva (CONSIGLIATA)
# Disabilita cache per HTML e API, abilita cache solo per asset statici

# Disabilita cache per la root e file HTML
location / {
    proxy_pass http://your-backend-server:5000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # Disabilita completamente la cache per HTML
    proxy_cache_bypass $http_pragma $http_authorization;
    proxy_no_cache $http_pragma $http_authorization;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
    add_header X-Cache-Status "BYPASS";
}

# Cache aggressiva solo per asset statici (JS, CSS, immagini)
location /assets/ {
    proxy_pass http://your-backend-server:5000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # Cache per 1 anno per asset con hash nel nome
    proxy_cache_valid 200 1y;
    add_header Cache-Control "public, max-age=31536000, immutable";
    add_header X-Cache-Status "HIT";
}

# Disabilita cache per API
location /api/ {
    proxy_pass http://your-backend-server:5000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # Nessuna cache per API
    proxy_cache_bypass 1;
    proxy_no_cache 1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}

# ============================================================================

# OPZIONE 2: Disabilita completamente la cache (SOLUZIONE TEMPORANEA)
# Usa questa opzione se l'opzione 1 non funziona

# proxy_cache off;
# add_header Cache-Control "no-cache, no-store, must-revalidate";
# add_header Pragma "no-cache";
# add_header Expires "0";

# ============================================================================

# OPZIONE 3: Solo per debugging - aggiungi header di debug
# Aggiungi queste righe per vedere cosa sta succedendo

# add_header X-Cache-Status $upstream_cache_status;
# add_header X-Proxy-Cache $upstream_cache_status;
# add_header X-Debug-Time $time_iso8601;

# ============================================================================

# ISTRUZIONI:
# 1. Accedi alla dashboard di Nginx Proxy Manager
# 2. Vai su "Proxy Hosts"
# 3. Clicca sui tre puntini del tuo host (gps.stgallazzi70.mynetgear.com)
# 4. Seleziona "Edit"
# 5. Vai sulla tab "Advanced"
# 6. Incolla una delle configurazioni sopra nella sezione "Custom Nginx Configuration"
# 7. Sostituisci "your-backend-server:5000" con l'IP e porta del tuo server
# 8. Clicca "Save"
# 9. Riavvia il container Nginx Proxy Manager se necessario

# NOTA: Se usi Docker, puoi riavviare il container con:
# docker restart nginx-proxy-manager
# oppure
# docker restart $(docker ps --format "table {{.Names}}" | grep -i nginx | head -1)
