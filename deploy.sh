#!/bin/bash

# Deploy script for Linux/Debian
# This script cleans, builds, and starts the application in production mode

set -e  # Exit on any error

echo "🚀 Starting deployment process for Linux/Debian..."

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed or not in PATH"
    exit 1
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed or not in PATH"
    exit 1
fi

echo "📋 Node.js version: $(node --version)"
echo "📋 npm version: $(npm --version)"

# Clean dist directory
echo "🧹 Cleaning dist directory..."
if [ -d "dist" ]; then
    rm -rf dist
    echo "✅ Dist directory cleaned"
else
    echo "ℹ️  Dist directory does not exist, skipping cleanup"
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Build the application
echo "🔨 Building application..."
npm run build

# Check if build was successful
if [ ! -f "dist/index.js" ]; then
    echo "❌ Build failed - dist/index.js not found"
    exit 1
fi

echo "✅ Build completed successfully"

# Clear Nginx cache if script exists
if [ -f "scripts/clear-nginx-cache.sh" ]; then
    echo "🧹 Clearing Nginx Proxy Manager cache..."
    bash scripts/clear-nginx-cache.sh
else
    echo "⚠️  Cache clearing script not found, skipping cache cleanup"
fi

# Start the production server
echo "🚀 Starting production server..."
export NODE_ENV=production
exec node dist/index.js
