# Script PowerShell per pulire la cache di Nginx Proxy Manager
# Questo script deve essere eseguito dopo ogni deploy per evitare problemi di cache

Write-Host "🧹 Pulizia cache Nginx Proxy Manager..." -ForegroundColor Cyan

# Metodo 1: Se stai usando Docker per Nginx Proxy Manager
if (Get-Command docker -ErrorAction SilentlyContinue) {
    Write-Host "Cercando container Nginx Proxy Manager..." -ForegroundColor Yellow
    
    # Cerca container di Nginx Proxy Manager
    $npmContainers = docker ps --format "table {{.Names}}" | Select-String -Pattern "nginx|proxy" | Select-Object -First 1
    
    if ($npmContainers) {
        $containerName = $npmContainers.ToString().Trim()
        Write-Host "Trovato container Nginx Proxy Manager: $containerName" -ForegroundColor Green
        Write-Host "Restart container..." -ForegroundColor Yellow
        docker restart $containerName
        Write-Host "✅ Container Nginx Proxy Manager riavviato" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Nessun container Nginx Proxy Manager trovato" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️  Docker non trovato" -ForegroundColor Yellow
}

# Metodo 2: Pulizia cache applicazione
Write-Host "Pulizia cache applicazione..." -ForegroundColor Yellow

if (Test-Path "dist") {
    # Aggiungi timestamp al build per forzare cache-busting
    $timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
    Write-Host "Build timestamp: $timestamp" -ForegroundColor Cyan
    
    # Se esiste un file di configurazione cache, aggiornalo
    if (Test-Path "dist/public/index.html") {
        $indexContent = Get-Content "dist/public/index.html" -Raw
        $indexContent = $indexContent -replace 'build-timestamp" content="\d*"', "build-timestamp`" content=`"$timestamp`""
        Set-Content "dist/public/index.html" -Value $indexContent -NoNewline
        Write-Host "✅ Timestamp aggiornato in index.html" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "🎉 Pulizia cache completata!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Passi manuali da seguire in Nginx Proxy Manager:" -ForegroundColor Cyan
Write-Host "1. Accedi alla dashboard di Nginx Proxy Manager"
Write-Host "2. Vai su 'Proxy Hosts'"
Write-Host "3. Modifica il tuo host gps.stgallazzi70.mynetgear.com"
Write-Host "4. Nella tab 'Advanced', aggiungi queste direttive:" -ForegroundColor Yellow
Write-Host ""
Write-Host "   # Disabilita cache per HTML" -ForegroundColor White
Write-Host "   location / {" -ForegroundColor White
Write-Host "       proxy_cache_bypass `$http_pragma `$http_authorization;" -ForegroundColor White
Write-Host "       proxy_no_cache `$http_pragma `$http_authorization;" -ForegroundColor White
Write-Host "       add_header Cache-Control `"no-cache, no-store, must-revalidate`";" -ForegroundColor White
Write-Host "       add_header Pragma `"no-cache`";" -ForegroundColor White
Write-Host "       add_header Expires `"0`";" -ForegroundColor White
Write-Host "   }" -ForegroundColor White
Write-Host ""
Write-Host "   # Cache solo per asset statici" -ForegroundColor White
Write-Host "   location /assets/ {" -ForegroundColor White
Write-Host "       proxy_cache_valid 200 1y;" -ForegroundColor White
Write-Host "       add_header Cache-Control `"public, max-age=31536000, immutable`";" -ForegroundColor White
Write-Host "   }" -ForegroundColor White
Write-Host ""
Write-Host "5. Salva e ricarica la configurazione"
Write-Host ""
Write-Host "🔄 Oppure prova a disabilitare completamente la cache:" -ForegroundColor Cyan
Write-Host "   - Vai su 'Advanced' nel tuo Proxy Host"
Write-Host "   - Aggiungi: proxy_cache off;" -ForegroundColor White

# Pausa per permettere di leggere le istruzioni
Write-Host ""
Write-Host "Premi un tasto per continuare..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
