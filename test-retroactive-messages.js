/**
 * Script di test per verificare il nuovo sistema di buffer dei messaggi retroattivi
 * Simula messaggi che arrivano fuori sequenza temporale
 */

import net from 'net';

// Configurazione
const SERVER_HOST = 'localhost';
const SERVER_PORT = 8090;
const TEST_IMEI = '123456789012345';

// Messaggi di test con timestamp fuori sequenza (simulano messaggi retroattivi)
const testMessages = [
  {
    timestamp: new Date('2025-01-30T10:00:00Z'), // Messaggio più vecchio
    data: createTestMessage(TEST_IMEI, '2025-01-30T10:00:00Z', 45.4642, 9.1900, 50)
  },
  {
    timestamp: new Date('2025-01-30T10:15:00Z'), // Messaggio nel mezzo
    data: createTestMessage(TEST_IMEI, '2025-01-30T10:15:00Z', 45.4700, 9.1950, 60)
  },
  {
    timestamp: new Date('2025-01-30T10:05:00Z'), // Messaggio che arriva per ultimo ma è cronologicamente secondo
    data: createTestMessage(TEST_IMEI, '2025-01-30T10:05:00Z', 45.4650, 9.1920, 55)
  },
  {
    timestamp: new Date('2025-01-30T10:25:00Z'), // Messaggio con gap > 5 minuti (dovrebbe creare nuovo viaggio)
    data: createTestMessage(TEST_IMEI, '2025-01-30T10:25:00Z', 45.4800, 9.2000, 40)
  }
];

function createTestMessage(imei, timestamp, lat, lng, speed) {
  // Simula il formato del protocollo asterisco corretto
  // Formato: *maker,serial,V1,time,validity,lat,latDir,long,longDir,speed,dir,date,vehicleStatus,...#
  const date = new Date(timestamp);
  const timeStr = date.toISOString().split('T')[1].replace(/:/g, '').split('.')[0]; // HHMMSS
  const dateStr = String(date.getDate()).padStart(2, '0') +
                  String(date.getMonth() + 1).padStart(2, '0') +
                  String(date.getFullYear()).slice(-2); // DDMMYY

  // Converti coordinate decimali in formato DDMM.MMMM
  const latDeg = Math.floor(Math.abs(lat));
  const latMin = (Math.abs(lat) - latDeg) * 60;
  const latStr = String(latDeg).padStart(2, '0') + latMin.toFixed(4).padStart(7, '0');

  const lngDeg = Math.floor(Math.abs(lng));
  const lngMin = (Math.abs(lng) - lngDeg) * 60;
  const lngStr = String(lngDeg).padStart(3, '0') + lngMin.toFixed(4).padStart(7, '0');

  return `*MAKER,${imei},V1,${timeStr},A,${latStr},N,${lngStr},E,${speed},0,${dateStr},F,0,0,0,0,0#`;
}

async function runTest() {
  console.log('🧪 Starting retroactive messages test...');
  console.log(`📡 Connecting to ${SERVER_HOST}:${SERVER_PORT}`);
  
  const client = new net.Socket();
  
  return new Promise((resolve, reject) => {
    client.connect(SERVER_PORT, SERVER_HOST, () => {
      console.log('✅ Connected to server');
      
      // Invia i messaggi in ordine casuale (simula messaggi retroattivi)
      let messageIndex = 0;
      
      const sendNextMessage = () => {
        if (messageIndex >= testMessages.length) {
          console.log('📦 All messages sent, waiting 2 seconds before disconnecting...');
          setTimeout(() => {
            console.log('🔌 Disconnecting to trigger buffer flush...');
            client.destroy();
          }, 2000);
          return;
        }
        
        const message = testMessages[messageIndex];
        console.log(`📤 Sending message ${messageIndex + 1}/${testMessages.length}:`);
        console.log(`   Timestamp: ${message.timestamp.toISOString()}`);
        console.log(`   Data: ${message.data}`);
        
        client.write(message.data);
        messageIndex++;
        
        // Invia il prossimo messaggio dopo un breve delay
        setTimeout(sendNextMessage, 1000);
      };
      
      // Inizia a inviare i messaggi
      sendNextMessage();
    });
    
    client.on('data', (data) => {
      console.log('📥 Server response:', data.toString());
    });
    
    client.on('close', () => {
      console.log('🔌 Connection closed');
      console.log('✅ Test completed! Check server logs for buffer processing.');
      resolve();
    });
    
    client.on('error', (err) => {
      console.error('❌ Connection error:', err);
      reject(err);
    });
  });
}

// Esegui il test
runTest().catch(console.error);
