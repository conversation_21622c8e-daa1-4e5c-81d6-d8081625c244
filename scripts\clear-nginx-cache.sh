#!/bin/bash

# Script per pulire la cache di Nginx Proxy Manager
# Questo script deve essere eseguito dopo ogni deploy per evitare problemi di cache

echo "🧹 Pulizia cache Nginx Proxy Manager..."

# Metodo 1: Prova a pulire la cache di Nginx se accessibile
if [ -d "/var/cache/nginx" ]; then
    echo "Pulizia cache Nginx locale..."
    sudo rm -rf /var/cache/nginx/*
    echo "✅ Cache Nginx locale pulita"
fi

# Metodo 2: Restart del servizio Nginx se disponibile
if systemctl is-active --quiet nginx; then
    echo "Restart servizio Nginx..."
    sudo systemctl reload nginx
    echo "✅ Nginx ricaricato"
fi

# Metodo 3: Se stai usando Docker per Nginx Proxy Manager
if command -v docker &> /dev/null; then
    # Cerca container di Nginx Proxy Manager
    NPM_CONTAINER=$(docker ps --format "table {{.Names}}" | grep -i "nginx\|proxy" | head -1)
    if [ ! -z "$NPM_CONTAINER" ]; then
        echo "Trovato container Nginx Proxy Manager: $NPM_CONTAINER"
        echo "Restart container..."
        docker restart "$NPM_CONTAINER"
        echo "✅ Container Nginx Proxy Manager riavviato"
    else
        echo "⚠️  Nessun container Nginx Proxy Manager trovato"
    fi
fi

# Metodo 4: Pulizia cache applicazione
echo "Pulizia cache applicazione..."
if [ -d "dist" ]; then
    # Aggiungi timestamp al build per forzare cache-busting
    TIMESTAMP=$(date +%s)
    echo "Build timestamp: $TIMESTAMP"
    
    # Se esiste un file di configurazione cache, aggiornalo
    if [ -f "dist/public/index.html" ]; then
        sed -i "s/build-timestamp\" content=\"[0-9]*\"/build-timestamp\" content=\"$TIMESTAMP\"/" dist/public/index.html
        echo "✅ Timestamp aggiornato in index.html"
    fi
fi

echo ""
echo "🎉 Pulizia cache completata!"
echo ""
echo "📋 Passi manuali da seguire in Nginx Proxy Manager:"
echo "1. Accedi alla dashboard di Nginx Proxy Manager"
echo "2. Vai su 'Proxy Hosts'"
echo "3. Modifica il tuo host (gps.stgallazzi70.mynetgear.com)"
echo "4. Nella tab 'Advanced', aggiungi queste direttive:"
echo ""
echo "   # Disabilita cache per HTML"
echo "   location / {"
echo "       proxy_cache_bypass \$http_pragma \$http_authorization;"
echo "       proxy_no_cache \$http_pragma \$http_authorization;"
echo "       add_header Cache-Control \"no-cache, no-store, must-revalidate\";"
echo "       add_header Pragma \"no-cache\";"
echo "       add_header Expires \"0\";"
echo "   }"
echo ""
echo "   # Cache solo per asset statici"
echo "   location /assets/ {"
echo "       proxy_cache_valid 200 1y;"
echo "       add_header Cache-Control \"public, max-age=31536000, immutable\";"
echo "   }"
echo ""
echo "5. Salva e ricarica la configurazione"
echo ""
echo "🔄 Oppure prova a disabilitare completamente la cache:"
echo "   - Vai su 'Advanced' nel tuo Proxy Host"
echo "   - Aggiungi: proxy_cache off;"
