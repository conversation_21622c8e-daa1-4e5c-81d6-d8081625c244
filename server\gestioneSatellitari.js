/**
 * Satellite message decoder
 * This file handles the decoding of messages received from satellite devices
 */

class SatelliteMessageDecoder {
  constructor() {
    // Define message protocols and formats here
    this.protocols = {
      // Add different satellite device protocols as needed
      default: this.decodeDefaultProtocol.bind(this)
    };
  }

  /**
   * Main decode function - routes to appropriate protocol decoder
   * @param {Buffer} rawData - Raw data received from TCP connection
   * @param {string} imei - IMEI of the device (if known)
   * @returns {Object|null} Decoded message object or null if invalid
   */
  decodeMessage(rawData, imei = null) {
    try {
      // Se il messaggio è binario, lo gestisco come buffer, altrimenti come stringa
      let dataString = rawData.toString('utf8').trim();
      if (dataString.startsWith('*')) {
        return this.decodeAsteriskProtocol(dataString);
      } else if (dataString.startsWith('$')) {
        return this.decodeDollarProtocol(rawData);
      } else {
        // Default protocol fallback
        return this.protocols.default(dataString, imei);
      }
    } catch (error) {
      console.error('Error decoding satellite message:', error);
      return null;
    }
  }

  /**
   * Default protocol decoder
   * Expected format: IMEI,TIMESTAMP,LAT,LNG,SPEED,DIRECTION,BATTERY,STATUS
   * Example: 354123456789012,2024-01-15T14:30:25.000Z,45.4642,9.1900,65,45,85,Fixed
   */
  decodeDefaultProtocol(dataString, knownImei = null) {
    const parts = dataString.split(',');
    
    if (parts.length < 8) {
      throw new Error(`Invalid message format: expected 8 parts, got ${parts.length}`);
    }

    const [imei, timestamp, lat, lng, speed, direction, battery, status] = parts;

    // Validate IMEI format (15 digits)
    if (!/^\d{15}$/.test(imei)) {
      throw new Error(`Invalid IMEI format: ${imei}`);
    }

    // Parse and validate coordinates
    const latitude = parseFloat(lat);
    const longitude = parseFloat(lng);
    
    if (isNaN(latitude) || isNaN(longitude)) {
      throw new Error(`Invalid coordinates: ${lat}, ${lng}`);
    }

    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      throw new Error(`Coordinates out of range: ${latitude}, ${longitude}`);
    }

    // Parse other numeric values
    const speedKmh = parseFloat(speed);
    const directionDegrees = parseFloat(direction);
    const batteryPercent = parseInt(battery, 10);

    if (isNaN(speedKmh) || speedKmh < 0) {
      throw new Error(`Invalid speed: ${speed}`);
    }

    if (isNaN(directionDegrees) || directionDegrees < 0 || directionDegrees >= 360) {
      throw new Error(`Invalid direction: ${direction}`);
    }

    if (isNaN(batteryPercent) || batteryPercent < 0 || batteryPercent > 100) {
      throw new Error(`Invalid battery percentage: ${battery}`);
    }

    // Validate status
    const validStatuses = ['Fixed', 'No Fixed', 'Start', 'End'];
    if (!validStatuses.includes(status)) {
      throw new Error(`Invalid status: ${status}. Must be one of: ${validStatuses.join(', ')}`);
    }

    // Parse timestamp
    const satelliteTimestamp = new Date(timestamp);
    if (isNaN(satelliteTimestamp.getTime())) {
      throw new Error(`Invalid timestamp: ${timestamp}`);
    }

    return {
      imei,
      satelliteTimestamp,
      latitude,
      longitude,
      speed: speedKmh,
      direction: directionDegrees,
      batteryPercentage: batteryPercent,
      status
    };
  }

  /**
   * Decoder per messaggi che iniziano con '*'
   * Esempio: *maker,serial,V1,time,validity,lat,latDir,long,longDir,speed,dir,date,vehicleStatus,...#
   */
  decodeAsteriskProtocol(dataString) {
    // Rimuovo * e # (inizio/fine)
    const parts = dataString.slice(1, -1).split(',');
    if (parts.length < 13 || parts[2] !== 'V1') {
      throw new Error('Formato heartbeat non valido');
    }
    const maker = parts[0];
    const serialNumber = parts[1];
    const time = parts[3];
    const validity = parts[4];
    const latitude = parts[5];
    const longitude = parts[7];
    const speed = parts[9];
    const direction = parts[10];
    const date = parts[11];
    // Calcolo latitudine
    const latDeg = parseInt(latitude.substring(0, 2));
    const latMin = parseFloat(latitude.substring(2)) / 60;
    const decimalLatitude = latDeg + latMin;
    // Calcolo longitudine
    const longDeg = parseInt(longitude.substring(0, 3));
    const longMin = parseFloat(longitude.substring(3)) / 60;
    const decimalLongitude = longDeg + longMin;
    // Data e ora
    const timedata = new Date(
      2000 + parseInt(date.slice(4)),
      parseInt(date.slice(2, 4)) - 1,
      parseInt(date.slice(0, 2)),
      parseInt(time.slice(0, 2)),
      parseInt(time.slice(2, 4)),
      parseInt(time.slice(4))
    );
    return {
      imei: serialNumber,
      satelliteTimestamp: timedata,
      latitude: decimalLatitude,
      longitude: decimalLongitude,
      speed: Math.round(parseFloat(speed)),
      direction: parseInt(direction),
      batteryPercentage: 0, // Non disponibile
      status: validity === 'A' ? 'Fixed' : 'No Fixed'
    };
  }

  /**
   * Decoder per messaggi che iniziano con '$' (binari)
   */
  decodeDollarProtocol(rawData) {
    const byteArray = rawData;
    // IMEI/seriale: byte 1-6
    const serialNumber = byteArray.slice(1, 6).toString('hex');
    // Data e ora
    const timedata = new Date(
      2000 + parseInt(byteArray.slice(11, 12).toString('hex')),
      parseInt(byteArray.slice(10, 11).toString('hex')) - 1,
      parseInt(byteArray.slice(9, 10).toString('hex')),
      parseInt(byteArray.slice(6, 7).toString('hex')),
      parseInt(byteArray.slice(7, 8).toString('hex')),
      parseInt(byteArray.slice(8, 9).toString('hex'))
    );
    // Status byte
    const statusByte = byteArray[21];
    const isValidGPS = (statusByte & 0x02) !== 0;
    // Latitudine
    let latitudeValue = byteArray.slice(12, 16).toString('hex');
    latitudeValue = latitudeValue.slice(0, 4) + '.' + latitudeValue.slice(4);
    const latDeg = parseInt(latitudeValue.substring(0, 2));
    const latMin = parseFloat(latitudeValue.substring(2)) / 60;
    const decimalLatitude = latDeg + latMin;
    // Longitudine
    let longitudeValue = byteArray.slice(17, 22).toString('hex');
    longitudeValue = longitudeValue.slice(0, 5) + '.' + longitudeValue.slice(5, 9);
    const longDeg = parseInt(longitudeValue.substring(0, 3));
    const longMin = parseFloat(longitudeValue.substring(3)) / 60;
    const decimalLongitude = longDeg + longMin;
    // Batteria
    const batteryValue = byteArray[16] || 0;
    // Velocità e direzione
    let spe_cou = byteArray.slice(22, 25);
    const speedKnots = (spe_cou[0] * 256 + spe_cou[1]) / 10;
    const speed = Math.round(speedKnots);
    const direction = spe_cou[2];
    return {
      imei: serialNumber,
      satelliteTimestamp: timedata,
      latitude: decimalLatitude,
      longitude: decimalLongitude,
      speed,
      direction,
      batteryPercentage: batteryValue,
      status: isValidGPS ? 'Fixed' : 'No Fixed'
    };
  }

  /**
   * Additional protocol decoders can be added here
   * For example:
   * - GT06 protocol
   * - Concox protocol
   * - TK103 protocol
   * etc.
   */
}

export { SatelliteMessageDecoder };
